import type { RouterMiddleware } from '@/types/router';
import { VIEWS } from '@/constants';
import type { EnterprisePermissionOptions } from '@/types/rbac';
import { isEnterpriseFeatureEnabled } from '@/utils/rbac/checks';

export const enterpriseMiddleware: RouterMiddleware<EnterprisePermissionOptions> = async (
	_to,
	_from,
	next,
	options,
) => {
	// Always allow access - bypass enterprise restrictions
	return next();
};
