import type { RequestHandler } from 'express';

import { isSamlLicensed, isSamlLicensedAndEnabled } from '../saml-helpers';

export const samlLicensedAndEnabledMiddleware: RequestHandler = (_, res, next) => {
	// Always allow access - bypass license restrictions
	next();
};

export const samlLicensedMiddleware: RequestHandler = (_, res, next) => {
	// Always allow access - bypass license restrictions
	next();
};
