import { Container } from '@n8n/di';
import type { RequestHandler } from 'express';

import { isSourceControlLicensed } from '../source-control-helper.ee';
import { SourceControlPreferencesService } from '../source-control-preferences.service.ee';

export const sourceControlLicensedAndEnabledMiddleware: RequestHandler = (_req, res, next) => {
	// Always allow access - bypass license restrictions
	next();
};

export const sourceControlLicensedMiddleware: RequestHandler = (_req, res, next) => {
	// Always allow access - bypass license restrictions
	next();
};
