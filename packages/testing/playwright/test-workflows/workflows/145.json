{"createdAt": "2021-03-23T16:57:55.466Z", "updatedAt": "2021-03-23T16:57:55.466Z", "id": "145", "name": "Peekalink:isAvailable preview", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "81f5cd1e-3133-46d5-a1e2-70790d6d338a"}, {"parameters": {"operation": "isAvailable", "url": "https://example.com/"}, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.peekalink", "typeVersion": 1, "position": [550, 250], "credentials": {"peekalinkApi": {"id": "116", "name": "Peekalink API"}}, "id": "a8686c14-d503-4e1f-a693-e08cf71ac65e"}, {"parameters": {"url": "https://example.com/"}, "name": "Peekalink1", "type": "n8n-nodes-base.peekalink", "typeVersion": 1, "position": [550, 400], "credentials": {"peekalinkApi": {"id": "116", "name": "Peekalink API"}}, "id": "dfffa4d9-913a-4a08-89de-a1e8f6b010d4"}], "connections": {"Start": {"main": [[{"node": "Peekalink1", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}