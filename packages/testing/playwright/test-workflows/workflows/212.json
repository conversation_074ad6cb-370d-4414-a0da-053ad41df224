{"createdAt": "2021-06-01T08:10:39.407Z", "updatedAt": "2021-11-12T13:08:28.130Z", "id": "212", "name": "Git:*", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [260, 300], "id": "f3928b9a-a76a-4b22-8c13-3eeb6ffc5dab"}, {"parameters": {"authentication": "gitPassword", "operation": "clone", "repositoryPath": "/tmp/nodeQA", "sourceRepository": "https://github.com/nodemationqa/nodeQA"}, "name": "Git", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [550, 250], "credentials": {"gitPassword": {"id": "193", "name": "Git creds"}}, "id": "73803968-9a2a-4408-9c35-cdb3fea58573"}, {"parameters": {"operation": "listConfig", "repositoryPath": "/tmp/nodeQA"}, "name": "Git1", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [700, 250], "notes": "CAP_RESULTS_LENGTH=1\nKEEP_ONLY_PROPERTIES=_file", "id": "c8635d48-0bbd-496a-8cd0-5cf43795707d"}, {"parameters": {"operation": "addConfig", "repositoryPath": "/tmp/nodeQA", "key": "user.email", "value": "<EMAIL>", "options": {"mode": "set"}}, "name": "Git2", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [850, 250], "id": "815b5424-cbc3-4f29-9f12-81e7538bb925"}, {"parameters": {"operation": "add", "repositoryPath": "/tmp/nodeQA", "pathsToAdd": "/tmp/nodeQA/gitnode_added_file.md"}, "name": "Git3", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [550, 450], "id": "d2110a10-424a-48ef-9bfa-5b6eb541f96c"}, {"parameters": {"operation": "status", "repositoryPath": "/tmp/nodeQA"}, "name": "Git4", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [700, 450], "id": "317691ba-e08a-4fcb-bb2d-771d14a13eb4"}, {"parameters": {"operation": "commit", "repositoryPath": "/tmp/nodeQA", "message": "=GitNode commit {{(new Date).toUTCString()}}", "options": {}}, "name": "Git5", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [850, 450], "id": "4ab79985-9685-4c47-b4c4-2f859ce036c0"}, {"parameters": {"repositoryPath": "/tmp/nodeQA", "limit": 1, "options": {}}, "name": "Git6", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [1000, 450], "id": "7ed13447-be70-41e3-b054-4107b175d5a8"}, {"parameters": {"operation": "addConfig", "repositoryPath": "/tmp/nodeQA", "key": "user.name", "value": "nodemationqa", "options": {"mode": "set"}}, "name": "Git7", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [1000, 250], "id": "8dabf47e-ba7c-460d-a2de-a4f54f5211e1"}, {"parameters": {"operation": "tag", "repositoryPath": "/tmp/nodeQA", "name": "=GitNode-tag-{{Date.now()}}"}, "name": "Git8", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [1150, 450], "id": "f4317e1d-694d-4d62-9b60-de9ef0e94418"}, {"parameters": {"operation": "pushTags", "repositoryPath": "/tmp/nodeQA"}, "name": "Git9", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [550, 650], "disabled": true, "id": "8ac866c6-9938-4d0f-9f54-6e3d5b1dbf11"}, {"parameters": {"operation": "push", "repositoryPath": "/tmp/nodeQA", "options": {}}, "name": "Git10", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [700, 650], "disabled": true, "id": "880d7f5f-744a-496a-9064-0a09a1dfe5ce"}, {"parameters": {"operation": "pull", "repositoryPath": "/tmp/nodeQA"}, "name": "Git11", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [850, 650], "disabled": true, "id": "5a5c9cd0-0e77-4ed9-960f-54f12d449683"}, {"parameters": {"operation": "fetch", "repositoryPath": "/tmp/nodeQA"}, "name": "Git12", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [1000, 650], "id": "e042e225-b5b6-4882-b879-d11d103c7004"}, {"parameters": {"operation": "userSetup", "repositoryPath": "/tmp/nodeQA"}, "name": "Git13", "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [1150, 650], "disabled": true, "id": "7b5a93b5-08bc-4449-a7ef-23232d6440a2"}, {"parameters": {"command": "if [ -d \"/tmp/nodeQA\" ]; then rm -Rf /tmp/nodeQA; fi"}, "name": "Delete existing dir", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [400, 250], "id": "312b664a-f6b4-4419-aae4-455930d7ca05"}, {"parameters": {"command": "=echo \"File added to repo at {{(new Date).toUTCString()}} using Git node\" > /tmp/nodeQA/gitnode_added_file.md"}, "name": "Add file to dir", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1150, 250], "id": "a5ad152a-056e-4d4e-81f8-626209e7cfb8"}], "connections": {"Git": {"main": [[{"node": "Git1", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Delete existing dir", "type": "main", "index": 0}]]}, "Git1": {"main": [[{"node": "Git2", "type": "main", "index": 0}]]}, "Git2": {"main": [[{"node": "Git7", "type": "main", "index": 0}]]}, "Git3": {"main": [[{"node": "Git4", "type": "main", "index": 0}]]}, "Git4": {"main": [[{"node": "Git5", "type": "main", "index": 0}]]}, "Git5": {"main": [[{"node": "Git6", "type": "main", "index": 0}]]}, "Git7": {"main": [[{"node": "Add file to dir", "type": "main", "index": 0}]]}, "Git6": {"main": [[{"node": "Git8", "type": "main", "index": 0}]]}, "Git8": {"main": [[{"node": "Git9", "type": "main", "index": 0}]]}, "Git9": {"main": [[{"node": "Git10", "type": "main", "index": 0}]]}, "Git10": {"main": [[{"node": "Git11", "type": "main", "index": 0}]]}, "Git11": {"main": [[{"node": "Git12", "type": "main", "index": 0}]]}, "Git12": {"main": [[{"node": "Git13", "type": "main", "index": 0}]]}, "Delete existing dir": {"main": [[{"node": "Git", "type": "main", "index": 0}]]}, "Add file to dir": {"main": [[{"node": "Git3", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}