{"createdAt": "2021-03-25T10:06:21.916Z", "updatedAt": "2023-03-16T11:39:51.000Z", "id": "155", "name": "AWSS3:Bucket:create getAll search:File:upload getAll download delete:Folder:create getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "d1457491-53e0-46a5-b33d-f950be72cd4d"}, {"parameters": {"resource": "bucket", "name": "={{$node[\"Set\"].json[\"bucketName\"]}}", "additionalFields": {}}, "name": "AWS S3", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [600, 300], "id": "07bca9de-36b9-4dcb-b61e-7fe54ac44273", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "bucketName", "value": "=Bucket{{Date.now()}}"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [450, 300], "id": "cc6144f2-31e2-47ff-bcb3-92d1c61d07fe"}, {"parameters": {"resource": "bucket", "operation": "getAll", "limit": 1}, "name": "AWS S", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [750, 300], "id": "d500a6b2-04ba-4786-bd4e-ab0432456caa", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"resource": "bucket", "operation": "search", "bucketName": "={{$node[\"Set\"].json[\"bucketName\"]}}", "limit": 1, "additionalFields": {}}, "name": "AWS S1", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1650, 310], "id": "ae1381cd-181d-41d4-a058-a276b9a860bc", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"operation": "upload", "bucketName": "={{$node[\"Set\"].json[\"bucketName\"]}}", "fileName": "n8n-logo.png", "additionalFields": {}}, "name": "AWS S2", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1050, 400], "id": "1ae1e324-2cbd-460b-ae07-45d76283b3de", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"functionCode": "item = {\n   json:{},\n   binary:{\n      data: {\n          data: '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', // Base64 encoded binary data (required)\n          mimeType: 'image/png',\n          fileExtension: 'png',\n          fileName: 'n8n-logo.png',\n      }\n   }\n}\n\nreturn [item];"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 400], "id": "07cbc362-2f04-49d7-bfe7-f6dc1449e633"}, {"parameters": {"operation": "getAll", "bucketName": "={{$node[\"Set\"].json[\"bucketName\"]}}", "limit": 1, "options": {}}, "name": "AWS S4", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1200, 400], "id": "6409d30f-f301-4455-915f-dca4ba380abd", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"bucketName": "={{$node[\"Set\"].json[\"bucketName\"]}}", "fileKey": "n8n-logo.png"}, "name": "AWS S5", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1350, 400], "id": "c92b350b-d8ca-434a-8c46-8f2af62100f4", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"operation": "copy", "sourcePath": "={{$node[\"Set\"].json[\"bucketName\"]}}/n8n-logo.png", "destinationPath": "={{$node[\"Set\"].json[\"bucketName\"]}}/copied-n8n-logo.png", "additionalFields": {}}, "name": "AWS S6", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1500, 400], "id": "f2d2014a-1b8a-4d40-88ea-c4033cc9cfcb", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "disabled": true}, {"parameters": {"operation": "delete", "bucketName": "={{$node[\"Set\"].json[\"bucketName\"]}}", "fileKey": "n8n-logo.png", "options": {}}, "name": "AWS S7", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1800, 400], "id": "844d7a3e-3e56-4111-9667-f985f4ef3cfc", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"resource": "folder", "bucketName": "={{$node[\"Set\"].json[\"bucketName\"]}}", "folderName": "={{$node[\"Set1\"].json[\"folderName\"]}}", "additionalFields": {}}, "name": "AWS S8", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [900, 150], "id": "2ede20ee-72aa-431a-bea6-b5831f4df5ae", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"resource": "folder", "operation": "getAll", "bucketName": "={{$node[\"Set\"].json[\"bucketName\"]}}", "limit": 1, "options": {}}, "name": "AWS S9", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1050, 150], "id": "6ec127d1-bb69-4d7d-9fcf-1ff928ae6b32", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"resource": "folder", "operation": "delete", "bucketName": "={{$node[\"Set\"].json[\"bucketName\"]}}", "folderKey": "={{$node[\"Set1\"].json[\"folderName\"]}}"}, "name": "AWS S10", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1200, 150], "id": "2697e526-d402-4d6b-8e87-6bd52cc39183", "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "folderName", "value": "=Folder{{Date.now()}}"}]}, "options": {}}, "name": "Set1", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [750, 150], "id": "a2c96217-5106-46a3-bbf9-ed8fb0062592"}, {"parameters": {"resource": "bucket", "operation": "delete", "name": "={{$node[\"Set\"].json[\"bucketName\"]}}"}, "id": "afab353f-4e1a-437b-9351-1fd786ededcc", "name": "AWS S31", "type": "n8n-nodes-base.awsS3", "typeVersion": 1, "position": [1980, 400], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}}], "connections": {"AWS S3": {"main": [[{"node": "AWS S", "type": "main", "index": 0}, {"node": "Set1", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "AWS S3", "type": "main", "index": 0}]]}, "AWS S": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "AWS S2", "type": "main", "index": 0}]]}, "AWS S2": {"main": [[{"node": "AWS S4", "type": "main", "index": 0}]]}, "AWS S4": {"main": [[{"node": "AWS S5", "type": "main", "index": 0}]]}, "AWS S5": {"main": [[{"node": "AWS S6", "type": "main", "index": 0}]]}, "AWS S6": {"main": [[{"node": "AWS S1", "type": "main", "index": 0}]]}, "AWS S1": {"main": [[{"node": "AWS S7", "type": "main", "index": 0}]]}, "AWS S8": {"main": [[{"node": "AWS S9", "type": "main", "index": 0}]]}, "AWS S9": {"main": [[{"node": "AWS S10", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "AWS S8", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "AWS S10": {"main": [[]]}, "AWS S7": {"main": [[{"node": "AWS S31", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": {}, "versionId": "75e8472d-f6c4-41de-9cc8-021c88f2e070", "triggerCount": 0, "tags": []}