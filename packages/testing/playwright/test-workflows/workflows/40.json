{"createdAt": "2021-02-18T14:57:48.780Z", "updatedAt": "2021-03-16T10:54:02.671Z", "id": "40", "name": "Gmail:Draft:create getAll get delete:Label:create getAll get delete:Message:send getAll get reply delete:MessageLabel:add remove", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [340, 400], "id": "05c88b78-968b-4f4c-a1e9-8898251d3f7d"}, {"parameters": {"subject": "=Draft created at {{(new Date()).toGMTString()}}", "message": "=Draft Test Body", "additionalFields": {}}, "name": "Gmail", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [600, 230], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "707afa42-fc6e-4117-84bc-29282319d5bb"}, {"parameters": {"operation": "getAll", "limit": 1, "additionalFields": {}}, "name": "Gmail1", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [760, 230], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "c73d9c21-ea4d-4976-ad4b-2ae4b0fada15"}, {"parameters": {"operation": "get", "messageId": "={{$node[\"Gmail\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Gmail2", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [920, 230], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "67666251-4a70-441a-a383-5582d297edd5"}, {"parameters": {"operation": "delete", "messageId": "={{$node[\"Gmail\"].json[\"id\"]}}"}, "name": "Gmail3", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1070, 230], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "a0b1960d-1a73-43a4-9978-7d3634051884"}, {"parameters": {"resource": "label", "name": "=TestLabel{{Date.now()}}"}, "name": "Gmail4", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [600, 380], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "f2ddc4ef-a79f-4764-ad1e-8fd88e8cc3d9"}, {"parameters": {"resource": "label", "operation": "getAll", "limit": 1}, "name": "Gmail5", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [760, 380], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "0f6821e9-a68e-46ce-905c-78b14eaa6435"}, {"parameters": {"resource": "label", "operation": "get", "labelId": "={{$node[\"Gmail4\"].json[\"id\"]}}"}, "name": "Gmail7", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [920, 380], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "c78804f2-5ea2-48e7-a98d-90ecb236d5b8"}, {"parameters": {"resource": "message", "subject": "=Subject  {{(new Date()).toGMTString()}}", "message": "=Email Body {{(new Date()).toGMTString()}}", "toList": ["<EMAIL>"], "additionalFields": {}}, "name": "Gmail8", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [610, 530], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "3f832f5f-94e0-4f86-a041-cbf30655c43b"}, {"parameters": {"resource": "message", "operation": "getAll", "limit": 1, "additionalFields": {}}, "name": "Gmail9", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1040, 530], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "fb256709-1d09-478c-8a18-331830cd6df1"}, {"parameters": {"resource": "message", "operation": "get", "messageId": "={{$node[\"Gmail8\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Gmail10", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1190, 530], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "5f15fa23-4149-410f-baa0-cca8123707b5"}, {"parameters": {"resource": "message", "operation": "delete", "messageId": "={{$node[\"Gmail8\"].json[\"id\"]}}"}, "name": "Gmail11", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1620, 530], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "33489121-9268-4bbf-ba17-e2673970d71c"}, {"parameters": {"resource": "message", "operation": "reply", "threadId": "={{$node[\"Gmail8\"].json[\"threadId\"]}}", "messageId": "={{$node[\"Gmail8\"].json[\"id\"]}}", "subject": "=Reply {{(new Date()).toGMTString()}}", "message": "=Reply body {{(new Date()).toGMTString()}}", "toList": ["<EMAIL>"], "additionalFields": {}}, "name": "Gmail12", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1330, 530], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "1b8da955-757e-4973-a350-58abac6c4822"}, {"parameters": {"resource": "messageLabel", "messageId": "={{$node[\"Gmail8\"].json[\"id\"]}}", "labelIds": ["SPAM"]}, "name": "Gmail13", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [740, 650], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "87698be8-16c6-496a-82fe-2ed1fcb3422a"}, {"parameters": {"resource": "message", "operation": "delete", "messageId": "={{$node[\"Gmail12\"].json[\"id\"]}}"}, "name": "Gmail14", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1890, 530], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "7f58e657-9bf8-4154-9353-39ac27d642cc"}, {"parameters": {"resource": "messageLabel", "operation": "remove", "messageId": "={{$node[\"Gmail8\"].json[\"id\"]}}", "labelIds": ["SPAM"]}, "name": "Gmail15", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [880, 650], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "0ffaee84-f0cf-42fc-a47d-0c9a25ae6b90"}, {"parameters": {"resource": "label", "operation": "delete", "labelId": "={{$node[\"Gmail4\"].json[\"id\"]}}"}, "name": "Gmail16", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1070, 380], "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}, "id": "998e0199-62ef-462b-a353-b7721c95ad46"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 Second", "type": "n8n-nodes-base.function", "position": [1470, 530], "typeVersion": 1, "id": "fea0314c-836f-43ea-8d3b-59fff652ad57"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 Second1", "type": "n8n-nodes-base.function", "position": [1760, 530], "typeVersion": 1, "id": "677eb37a-3a00-4f00-aa8a-cfc7b373cc1c"}], "connections": {"Gmail": {"main": [[{"node": "Gmail1", "type": "main", "index": 0}]]}, "Gmail1": {"main": [[{"node": "Gmail2", "type": "main", "index": 0}]]}, "Gmail2": {"main": [[{"node": "Gmail3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Gmail8", "type": "main", "index": 0}, {"node": "Gmail4", "type": "main", "index": 0}, {"node": "Gmail", "type": "main", "index": 0}]]}, "Gmail4": {"main": [[{"node": "Gmail5", "type": "main", "index": 0}]]}, "Gmail5": {"main": [[{"node": "Gmail7", "type": "main", "index": 0}]]}, "Gmail9": {"main": [[{"node": "Gmail10", "type": "main", "index": 0}]]}, "Gmail8": {"main": [[{"node": "Gmail13", "type": "main", "index": 0}]]}, "Gmail10": {"main": [[{"node": "Gmail12", "type": "main", "index": 0}]]}, "Gmail12": {"main": [[{"node": "Sleep 0.5 Second", "type": "main", "index": 0}]]}, "Gmail13": {"main": [[{"node": "Gmail15", "type": "main", "index": 0}]]}, "Gmail15": {"main": [[{"node": "Gmail9", "type": "main", "index": 0}]]}, "Gmail7": {"main": [[{"node": "Gmail16", "type": "main", "index": 0}]]}, "Gmail14": {"main": [[]]}, "Sleep 0.5 Second": {"main": [[{"node": "Gmail11", "type": "main", "index": 0}]]}, "Sleep 0.5 Second1": {"main": [[{"node": "Gmail14", "type": "main", "index": 0}]]}, "Gmail11": {"main": [[{"node": "Sleep 0.5 Second1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}