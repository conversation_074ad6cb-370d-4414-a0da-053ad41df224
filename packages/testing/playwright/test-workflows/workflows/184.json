{"createdAt": "2021-04-26T16:15:15.635Z", "updatedAt": "2021-05-20T16:56:10.192Z", "id": "184", "name": "Salesforce:Account:create get addNote getAll getSummary update delete:Case:create get addComment getAll getSummary update delete:Attachment:create get getAll getSummary update delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "66a1cb46-3115-4e3f-b986-589153ee056b"}, {"parameters": {"resource": "account", "name": "=Account{{Date.now()}}", "additionalFields": {}}, "name": "Salesforce", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [520, 190], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "5b1a4927-ea99-4ad0-9166-87de446b39e2"}, {"parameters": {"resource": "account", "operation": "get", "accountId": "={{$node[\"Salesforce\"].json[\"id\"]}}"}, "name": "Salesforce1", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [770, 190], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "********-42fb-4d9c-af75-f55fcac7a0e8"}, {"parameters": {"resource": "account", "operation": "addNote", "accountId": "={{$node[\"Salesforce\"].json[\"id\"]}}", "title": "=Note{{Date.now()}}", "options": {}}, "name": "Salesforce2", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [930, 190], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "3036a0b8-1700-4cac-afd9-0679461bbeb3"}, {"parameters": {"resource": "account", "operation": "getAll", "limit": 1, "options": {}}, "name": "Salesforce3", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1210, 190], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "3025fb7e-2d06-4174-b60b-e31ae414b56e"}, {"parameters": {"resource": "account", "operation": "getSummary"}, "name": "Salesforce4", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1360, 190], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "f9572f72-55e8-4e58-9aac-aebc4476a91b"}, {"parameters": {"resource": "account", "operation": "update", "accountId": "={{$node[\"Salesforce\"].json[\"id\"]}}", "updateFields": {"name": "=Updated{{$node[\"Salesforce1\"].json[\"Name\"]}}"}}, "name": "Salesforce5", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1650, 190], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "50385d4f-bfbf-49ba-a620-6372ce9eb7e2"}, {"parameters": {"resource": "account", "operation": "delete", "accountId": "={{$node[\"Salesforce\"].json[\"id\"]}}"}, "name": "Salesforce6", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1800, 190], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "2baac145-ccd2-421a-8e57-0312264f64c6"}, {"parameters": {"resource": "case", "type": "Other", "additionalFields": {"reason": "Installation", "subject": "=Subject{{Date.now()}}"}}, "name": "Salesforce7", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [530, 350], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "6f52f121-1906-4583-9ab5-ace8a85c21f1"}, {"parameters": {"resource": "case", "operation": "get", "caseId": "={{$node[\"Salesforce7\"].json[\"id\"]}}"}, "name": "Salesforce8", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [690, 350], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "5623e57f-6a90-46d4-b4b1-2a295c4a97be"}, {"parameters": {"resource": "case", "operation": "addComment", "caseId": "={{$node[\"Salesforce7\"].json[\"id\"]}}", "options": {"commentBody": "=Comment{{Date.now()}}"}}, "name": "Salesforce9", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [2490, 350], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "2fd4dca0-4bfa-41d2-971e-0bd469242d21"}, {"parameters": {"resource": "case", "operation": "getSummary"}, "name": "Salesforce10", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [2750, 350], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "93aa55e7-46e5-4576-a634-82ff1b94963b"}, {"parameters": {"resource": "case", "operation": "getAll", "limit": 1, "options": {}}, "name": "Salesforce11", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [2910, 350], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "3e12fbf3-547f-4444-84c8-82339fa49485"}, {"parameters": {"resource": "case", "operation": "update", "caseId": "={{$node[\"Salesforce7\"].json[\"id\"]}}", "updateFields": {"subject": "=Updated{{$node[\"Salesforce8\"].json[\"Subject\"]}}"}}, "name": "Salesforce12", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [3180, 350], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "dc93d012-04c6-4b5b-85c8-6a6cdd2c37f1"}, {"parameters": {"resource": "case", "operation": "delete", "caseId": "={{$node[\"Salesforce7\"].json[\"id\"]}}"}, "name": "Salesforce13", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [3340, 350], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "339d8458-fcf3-4717-8baf-33651e27e169"}, {"parameters": {"resource": "attachment", "parentId": "={{$node[\"Salesforce8\"].json[\"Id\"]}}", "name": "=Attachment{{Date.now()}}", "additionalFields": {}}, "name": "Salesforce14", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1260, 500], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "6cac533b-4965-4432-908e-5f9abf9d7e28"}, {"parameters": {"values": {"string": [{"name": "data", "value": "=Attachment example"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [800, 500], "id": "4e4a6a2b-ffb1-4ca1-93bd-39ace5d35c68"}, {"parameters": {"mode": "jsonToBinary", "options": {}}, "name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1, "position": [960, 500], "id": "2436fc2e-c730-4cfc-aeb9-0fbeda60b653"}, {"parameters": {"resource": "attachment", "operation": "get", "attachmentId": "={{$node[\"Salesforce14\"].json[\"id\"]}}"}, "name": "Salesforce15", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1410, 500], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "044ebdfa-a4ba-4c6f-a6b8-73e557b01f5a"}, {"parameters": {"resource": "attachment", "operation": "getAll", "limit": 1, "options": {}}, "name": "Salesforce16", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1710, 500], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "11d43928-769d-49b0-b0df-540b98dbb94e"}, {"parameters": {"resource": "attachment", "operation": "getSummary"}, "name": "Salesforce17", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1860, 500], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "677c0167-c2c3-43cd-87c8-6281f3bdbced"}, {"parameters": {"resource": "attachment", "operation": "update", "attachmentId": "={{$node[\"Salesforce14\"].json[\"id\"]}}", "updateFields": {"isPrivate": true}}, "name": "Salesforce18", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [2140, 500], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "0f9a0337-0b8e-4839-8507-4da0ae9cce8f"}, {"parameters": {"resource": "attachment", "operation": "delete", "attachmentId": "={{$node[\"Salesforce14\"].json[\"id\"]}}"}, "name": "Salesforce19", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [2290, 500], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "d3dee2a4-4a49-42c8-9965-8d3a4614dab7"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second", "type": "n8n-nodes-base.function", "position": [650, 190], "typeVersion": 1, "id": "82031d82-409d-4921-8fd7-bdb9f5b30e44"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second1", "type": "n8n-nodes-base.function", "position": [1070, 190], "typeVersion": 1, "id": "ca22ae1e-464b-4c60-9411-d953f6274def"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second2", "type": "n8n-nodes-base.function", "position": [1510, 190], "typeVersion": 1, "id": "b51888e4-e156-45dc-b04a-de238a40d59b"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second3", "type": "n8n-nodes-base.function", "position": [3050, 350], "typeVersion": 1, "id": "52d4818a-c5fa-4172-9a9d-754095a21f3b"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second4", "type": "n8n-nodes-base.function", "position": [2620, 350], "typeVersion": 1, "id": "8fdcf0f4-d8be-41dd-94c1-4c3802500701"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second5", "type": "n8n-nodes-base.function", "position": [1990, 500], "typeVersion": 1, "id": "98e604e1-ee39-43a5-8aad-53b423f88033"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second6", "type": "n8n-nodes-base.function", "position": [1580, 500], "typeVersion": 1, "id": "04e6ad3e-5031-4945-ac5e-84baaf8db3be"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second7", "type": "n8n-nodes-base.function", "position": [1120, 500], "typeVersion": 1, "id": "a61fabaa-a79e-4714-8910-4300cdb6c0b5"}], "connections": {"Salesforce": {"main": [[{"node": "Sleep 0.5 second", "type": "main", "index": 0}]]}, "Salesforce1": {"main": [[{"node": "Salesforce2", "type": "main", "index": 0}]]}, "Salesforce2": {"main": [[{"node": "Sleep 0.5 second1", "type": "main", "index": 0}]]}, "Salesforce3": {"main": [[{"node": "Salesforce4", "type": "main", "index": 0}]]}, "Salesforce4": {"main": [[{"node": "Sleep 0.5 second2", "type": "main", "index": 0}]]}, "Salesforce5": {"main": [[{"node": "Salesforce6", "type": "main", "index": 0}]]}, "Salesforce7": {"main": [[{"node": "Salesforce8", "type": "main", "index": 0}]]}, "Salesforce8": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Salesforce9": {"main": [[{"node": "Sleep 0.5 second4", "type": "main", "index": 0}]]}, "Salesforce10": {"main": [[{"node": "Salesforce11", "type": "main", "index": 0}]]}, "Salesforce11": {"main": [[{"node": "Sleep 0.5 second3", "type": "main", "index": 0}]]}, "Salesforce12": {"main": [[{"node": "Salesforce13", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Move Binary Data": {"main": [[{"node": "Sleep 0.5 second7", "type": "main", "index": 0}]]}, "Salesforce14": {"main": [[{"node": "Salesforce15", "type": "main", "index": 0}]]}, "Salesforce15": {"main": [[{"node": "Sleep 0.5 second6", "type": "main", "index": 0}]]}, "Salesforce16": {"main": [[{"node": "Salesforce17", "type": "main", "index": 0}]]}, "Salesforce17": {"main": [[{"node": "Sleep 0.5 second5", "type": "main", "index": 0}]]}, "Salesforce18": {"main": [[{"node": "Salesforce19", "type": "main", "index": 0}]]}, "Salesforce19": {"main": [[{"node": "Salesforce9", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Salesforce7", "type": "main", "index": 0}, {"node": "Salesforce", "type": "main", "index": 0}]]}, "Sleep 0.5 second": {"main": [[{"node": "Salesforce1", "type": "main", "index": 0}]]}, "Sleep 0.5 second1": {"main": [[{"node": "Salesforce3", "type": "main", "index": 0}]]}, "Sleep 0.5 second2": {"main": [[{"node": "Salesforce5", "type": "main", "index": 0}]]}, "Sleep 0.5 second3": {"main": [[{"node": "Salesforce12", "type": "main", "index": 0}]]}, "Sleep 0.5 second4": {"main": [[{"node": "Salesforce10", "type": "main", "index": 0}]]}, "Sleep 0.5 second5": {"main": [[{"node": "Salesforce18", "type": "main", "index": 0}]]}, "Sleep 0.5 second6": {"main": [[{"node": "Salesforce16", "type": "main", "index": 0}]]}, "Sleep 0.5 second7": {"main": [[{"node": "Salesforce14", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}