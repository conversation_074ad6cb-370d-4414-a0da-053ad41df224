{"createdAt": "2021-03-01T10:49:40.953Z", "updatedAt": "2021-03-01T10:49:40.953Z", "id": "83", "name": "RSS Feed", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "2abda6cf-70a8-40c3-9446-4f7f8233e54a"}, {"parameters": {"url": "https://www.producthunt.com/feed"}, "name": "RSS Feed Read", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1, "position": [450, 300], "id": "46f4bb0e-011d-499c-92bd-7b1e6c814133"}, {"parameters": {"functionCode": "if(items.length !== 50){\n  throw new Error('Invalid RSS feed result');\n}else{\nreturn [];\n}\n"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [650, 300], "notesInFlow": true, "notes": "Throw error if result.length !=50", "id": "f4a98301-2dbe-45b2-ac1d-97df0ac58b94"}], "connections": {"Start": {"main": [[{"node": "RSS Feed Read", "type": "main", "index": 0}]]}, "RSS Feed Read": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}