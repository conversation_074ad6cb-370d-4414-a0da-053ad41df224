{"createdAt": "2021-04-21T07:53:05.959Z", "updatedAt": "2021-04-21T07:53:05.959Z", "id": "179", "name": "uProc:Image:*:Security:*;", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "ea22a2a9-c7b7-4e4c-b89a-d123c4de7c63"}, {"parameters": {"group": "security", "tool": "checkNumberUuid", "uuid": "0c6a1543-6232-49d2-b50d-c1e70cd015a0", "additionalOptions": {}}, "name": "uProc1", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [450, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "cd9469be-7495-4963-9e72-2946c8c9026b"}, {"parameters": {"group": "security", "tool": "getDomainBlacklists", "domain": "n8n.io", "additionalOptions": {}}, "name": "uProc2", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [580, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "65d979d4-1b48-4e05-81ec-003ca73360ec"}, {"parameters": {"group": "security", "tool": "getIpBlacklists", "ip": "***********", "additionalOptions": {}}, "name": "uProc3", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [710, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "a93216f3-6d7e-4e8e-808f-2a9781cd54d1"}, {"parameters": {"group": "security", "luhn": "79927398713", "additionalOptions": {}}, "name": "uProc4", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [840, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "c850df47-d882-4253-9912-87781ce9cca5"}, {"parameters": {"group": "security", "tool": "checkPasswordStrong", "password": "randompassword", "additionalOptions": {}}, "name": "uProc5", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [970, 350], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "13527442-5599-40a4-aa97-69e88fc71dac"}, {"parameters": {"group": "image", "tool": "getBarcodeEncoded", "text": "n8n rocks!", "bcid": "auspost", "additionalOptions": {}}, "name": "uProc", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [450, 150], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "826bf485-a60a-4835-8290-1259daf44305"}, {"parameters": {"group": "image", "tool": "getImageExif", "url": "https://n8n.io/_nuxt/img/df5be1c.png", "additionalOptions": {}}, "name": "uProc8", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [840, 150], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "dcf2a843-5f5f-4f9f-ba27-1aaa9486dd58"}, {"parameters": {"group": "image", "tool": "getImageWithText", "text": "n8n rocks!", "url": "https://n8n.io/_nuxt/img/df5be1c.png", "size": "72", "additionalOptions": {}}, "name": "uProc9", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [970, 150], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "5a42b133-730f-4288-aed9-8ef6ab9f9579"}, {"parameters": {"group": "image", "tool": "getDomainLogo", "domain": "n8n.io", "additionalOptions": {}}, "name": "uProc10", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [580, 150], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "5a1a3be9-b954-4cab-8adc-a82fa4156780"}, {"parameters": {"group": "image", "tool": "getUrlScreenshot", "url": "n8n.io", "useragent": "n8n-agent", "width": "160", "fullpage": "no", "additionalOptions": {}}, "name": "uProc11", "type": "n8n-nodes-base.uproc", "typeVersion": 1, "position": [710, 150], "credentials": {"uprocApi": {"id": "142", "name": "uProc API creds"}}, "id": "65aa976a-1b8b-4ddf-bab6-f3308221ade0"}], "connections": {"uProc1": {"main": [[{"node": "uProc2", "type": "main", "index": 0}]]}, "uProc2": {"main": [[{"node": "uProc3", "type": "main", "index": 0}]]}, "uProc3": {"main": [[{"node": "uProc4", "type": "main", "index": 0}]]}, "uProc4": {"main": [[{"node": "uProc5", "type": "main", "index": 0}]]}, "uProc": {"main": [[{"node": "uProc10", "type": "main", "index": 0}]]}, "uProc8": {"main": [[{"node": "uProc9", "type": "main", "index": 0}]]}, "uProc9": {"main": [[]]}, "uProc10": {"main": [[{"node": "uProc11", "type": "main", "index": 0}]]}, "uProc11": {"main": [[{"node": "uProc8", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "uProc", "type": "main", "index": 0}, {"node": "uProc1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}