{"createdAt": "2024-03-04T20:35:31.373Z", "updatedAt": "2024-03-04T20:36:25.000Z", "id": "244", "name": "Agent:WorkflowTool", "active": false, "nodes": [{"parameters": {}, "id": "e01e31b1-1114-4384-b88d-522337a0010b", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1, "position": [200, 800]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.tool }}", "rightValue": "get_weather", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Weather"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "a164188f-3b5b-4c24-b1bb-e589f4f9219f", "leftValue": "={{ $json.tool }}", "rightValue": "get_events", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Events"}]}, "options": {}}, "id": "a8741e96-bff4-463c-9dd0-ffba5b9877b3", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [420, 800]}, {"parameters": {"name": "get_weather_data", "description": "Call this tool to get weather information for a specific city. Input should be a single string in format: \"$CITY, $COUNTRY\". So for example to get data about Prague, \"Prague, Czechia\".", "workflowId": "={{ $workflow.id }}", "fields": {"values": [{"name": "tool", "stringValue": "get_weather"}]}}, "id": "d35195a4-f609-467d-9cca-b659e5f76c2b", "name": "Get Weather", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1, "position": [620, 620]}, {"parameters": {"name": "get_evens", "description": "Call this tool to get upcoming events for a specific city. Input should be a single string in format: \"$CITY, $COUNTRY\". So for example to get data about Prague, \"Prague, Czechia\".", "workflowId": "={{ $workflow.id }}", "fields": {"values": [{"name": "tool", "stringValue": "get_events"}]}}, "id": "8214ad91-530b-49a1-874b-42d31d1d9e87", "name": "Get Events", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1, "position": [720, 620]}, {"parameters": {"assignments": {"assignments": [{"id": "fc61cf88-967d-4433-9cfd-7cdad1a43e75", "name": "response", "value": "={\n    \"created\": \"2024-03-04T09:26:23+01:00\",\n    \"symbolCode\": {\n        \"next1Hour\": \"fog\"\n    },\n    \"temperature\": {\n        \"value\": 5.1,\n        \"feelsLike\": 4\n    },\n    \"precipitation\": {\n        \"value\": 0.0\n    },\n    \"wind\": {\n        \"direction\": 275,\n        \"speed\": 1.7\n    },\n    \"status\": {\n        \"code\": \"Ok\"\n    }\n}", "type": "string"}]}, "options": {}}, "id": "cdb19b9d-9665-4b66-bda7-1b4f8191dad0", "name": "Edit Fields4", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [700, 760]}, {"parameters": {"assignments": {"assignments": [{"id": "0434695d-b245-4947-8b6e-7676a5c92904", "name": "response", "value": "=[\n    {\n        \"description\": \"***Movie Barf* is a new English friendly film night presented by film journalist and blogger <PERSON>, dedicated to screening a diverse variety of award-winning films both contemporary and classic. <PERSON>’s late night shows includes intriguing chats with various guests (in person or over Skype in the case of the international ones) and special drink offers at the bar.**\\n\\n*Dune: Part Two* / <PERSON> / <PERSON>, USA 2024 / 166 min – <PERSON> unites with <PERSON><PERSON> and the Fremen while seeking revenge against the conspirators who destroyed his family.\",\n        \"name\": \"Movie Barf: Dune – Part Two\",\n        \"endDate\": \"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\"\n    },\n    {\n        \"description\": \"<PERSON><PERSON><PERSON> will perform with the renewed band 5P on March 14 at the cultural house of Barikadník.\",\n        \"name\": \"<PERSON><PERSON><PERSON> & 5P\",\n        \"endDate\": \"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\"\n    },\n    {\n        \"description\": \"An insomniac office worker looking for a way to change his life crosses paths with a devil-may-care soap maker and they form an underground fight club that evolves into something much, much more...\",\n        \"name\": \"Fight Club\",\n        \"endDate\": \"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\"\n    },\n    {\n        \"description\": \"From filmmaker <PERSON><PERSON><PERSON>nthim<PERSON> and producer <PERSON> <PERSON> comes the incredible tale and fantastical evolution of <PERSON> <PERSON> (<PERSON>), a young woman brought back to life by the brilliant and unorthodox scientist <PERSON>. <PERSON>win <PERSON> (<PERSON> <PERSON>foe). Under <PERSON>'s protection, <PERSON> is eager to learn. <PERSON>ry for the worldliness she is lacking, she runs off with <PERSON> <PERSON>dderburn (<PERSON> <PERSON>uffalo), a slick and debauched lawyer, on a whirlwind adventure across the continents. <PERSON> from the prejudices of her times, <PERSON> grows steadfast in her purpose to stand for equality and liberation.\",\n        \"name\": \"Poor Things\",\n        \"endDate\": \"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\"\n    },\n    {\n        \"description\": \"Concert of Bharata Rajnošek, who decided to do something very brave - pay tribute to king of the pop, Michael Jackson in jazz.\",\n        \"name\": \"Tribute to World Legends: Michael Jackson\",\n        \"endDate\": \"Mon Mar 04 2024 23:59:59 GMT+0100 (Central European Standard Time)\"\n    }\n]", "type": "string"}]}, "options": {}}, "id": "44e58855-7ff7-4f80-9092-f6ed72274874", "name": "Edit Fields5", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [700, 920]}, {"parameters": {"model": "gpt-4-turbo-preview", "options": {"temperature": 0}}, "id": "2fecb46d-8bff-4884-9e73-bd787a6b1ece", "name": "OpenAI Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [480, 620], "credentials": {"openAiApi": {"id": "Zak03cqeLUOsgkFI", "name": "OpenAi account"}}, "notes": "IGNORED_PROPERTIES=messages"}, {"parameters": {"assignments": {"assignments": [{"id": "414caf45-02aa-4c0a-9cdb-e6da9ec03d80", "name": "has_weather", "value": "={{ $json.output.includes('5.1') }}", "type": "boolean"}, {"id": "4f055fa4-10eb-4b7e-b1dc-37a7ef7185fc", "name": "has_movie", "value": "={{ $json.output.includes('Dune') }}", "type": "boolean"}]}, "options": {}}, "id": "********-d991-459a-8ad6-07396f0c629e", "name": "Edit Fields6", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [840, 460]}, {"parameters": {"agent": "reActAgent", "promptType": "define", "text": "Help me plan my day in Berlin, Germany\n1. Check current the weather \n2. Get the upcoming events\n3. Respond with weather and details about the upcoming events\n\nEach tool should only be called once.", "options": {}}, "id": "367e796c-9c91-42a6-9585-bb9f19c2969a", "name": "AI Agent4", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.5, "position": [460, 460]}, {"parameters": {}, "id": "84833733-c87b-442c-9be1-26315056a205", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, 460]}], "connections": {"Execute Workflow Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Edit Fields4", "type": "main", "index": 0}], [{"node": "Edit Fields5", "type": "main", "index": 0}]]}, "Get Weather": {"ai_tool": [[{"node": "AI Agent4", "type": "ai_tool", "index": 0}]]}, "Get Events": {"ai_tool": [[{"node": "AI Agent4", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model4": {"ai_languageModel": [[{"node": "AI Agent4", "type": "ai_languageModel", "index": 0}]]}, "AI Agent4": {"main": [[{"node": "Edit Fields6", "type": "main", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "AI Agent4", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "058fe472-fd5e-4fab-8f4d-1363d4adbbdb", "triggerCount": 0, "tags": []}