{"createdAt": "2021-05-25T13:01:33.223Z", "updatedAt": "2021-05-26T13:12:05.308Z", "id": "208", "name": "RocketChat:Chat:postMessage", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "57c39841-256a-4e6a-b301-af2c97c0a0a8"}, {"parameters": {"channel": "general", "text": "=Message {{(new Date).toISOString()}}", "options": {"emoji": ":smile:"}, "attachments": [{"color": "#ff6d5a", "title": "Attachment title"}]}, "name": "RocketChat", "type": "n8n-nodes-base.rocketchat", "typeVersion": 1, "position": [500, 300], "credentials": {"rocketchatApi": {"id": "184", "name": "Rocket API creds"}}, "id": "c6b5e0b7-4ead-44e3-8ae4-42de8159dd99"}], "connections": {"Start": {"main": [[{"node": "RocketChat", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}