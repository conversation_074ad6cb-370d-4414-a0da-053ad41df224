{"createdAt": "2021-07-08T14:10:14.879Z", "updatedAt": "2021-07-09T14:05:22.996Z", "id": "227", "name": "Flow:Task:create get getAll update", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "077a12f0-e2c8-4990-bf0c-3705208c8ada"}, {"parameters": {"workspaceId": "473181", "name": "=Task{{Date.now()}}", "additionalFields": {"noteContent": "Task content", "noteMimeType": "text/plain", "position": 1}}, "name": "Flow", "type": "n8n-nodes-base.flow", "typeVersion": 1, "position": [450, 300], "credentials": {"flowApi": {"id": "220", "name": "Flow API creds"}}, "id": "2a76f52f-0b77-4c76-bbca-c54e6581d13e"}, {"parameters": {"operation": "get", "taskId": "={{$node[\"Flow\"].json[\"id\"]}}", "filters": {}}, "name": "Flow1", "type": "n8n-nodes-base.flow", "typeVersion": 1, "position": [600, 300], "credentials": {"flowApi": {"id": "220", "name": "Flow API creds"}}, "id": "c1f80e78-bad4-457f-8b00-a239a070ca09"}, {"parameters": {"operation": "getAll", "limit": 1, "filters": {}}, "name": "Flow2", "type": "n8n-nodes-base.flow", "typeVersion": 1, "position": [750, 300], "credentials": {"flowApi": {"id": "220", "name": "Flow API creds"}}, "id": "5ea201ba-164f-4ed0-be1d-d893930d6568"}, {"parameters": {"operation": "update", "workspaceId": "473181", "taskId": "={{$node[\"Flow\"].json[\"id\"]}}", "updateFields": {"name": "=Updated{{$node[\"Flow1\"].json[\"task\"][\"name\"]}}", "completed": true}}, "name": "Flow3", "type": "n8n-nodes-base.flow", "typeVersion": 1, "position": [900, 300], "credentials": {"flowApi": {"id": "220", "name": "Flow API creds"}}, "id": "df48712d-b64d-43b8-8493-f053d5b52804"}], "connections": {"Flow": {"main": [[{"node": "Flow1", "type": "main", "index": 0}]]}, "Flow1": {"main": [[{"node": "Flow2", "type": "main", "index": 0}]]}, "Flow2": {"main": [[{"node": "Flow3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Flow", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}