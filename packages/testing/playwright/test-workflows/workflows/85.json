{"createdAt": "2021-03-02T10:23:05.991Z", "updatedAt": "2021-05-25T13:21:54.785Z", "id": "85", "name": "Linkedin:Post:create", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [200, 300], "id": "92f57774-0f92-4eaf-89e9-e8c33dd21cfe"}, {"parameters": {"postAs": "person", "person": "gpZ0alGajT", "text": "=Test Post created by person at {{new Date()}}", "additionalFields": {"visibility": "CONNECTIONS"}}, "name": "LinkedIn", "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [550, 200], "credentials": {"linkedInOAuth2Api": {"id": "74", "name": "LinkedIn OAuth2 creds"}}, "id": "63e56abf-2054-4691-99d7-e639d2a1af45"}, {"parameters": {"postAs": "person", "person": "gpZ0alGajT", "text": "=Test Post created by person at {{new Date()}}", "shareMediaCategory": "IMAGE", "additionalFields": {"title": "=ImagePost {{Date.now()}}", "visibility": "CONNECTIONS"}}, "name": "LinkedIn1", "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [560, 350], "credentials": {"linkedInOAuth2Api": {"id": "74", "name": "LinkedIn OAuth2 creds"}}, "id": "a6a0d2d6-f399-41a3-88fe-a4b0c2d5512e"}, {"parameters": {"filePath": "/tmp/n8n-logo.png"}, "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [420, 350], "id": "e80c32c0-f983-46cd-becc-e030b93732c7"}], "connections": {"Read Binary File": {"main": [[{"node": "LinkedIn1", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}, {"node": "LinkedIn", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}