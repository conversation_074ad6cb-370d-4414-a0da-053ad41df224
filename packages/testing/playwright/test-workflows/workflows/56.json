{"createdAt": "2021-02-22T11:29:46.504Z", "updatedAt": "2021-02-22T13:29:41.718Z", "id": "56", "name": "Trello:Board:*List:*Card:*Label:*CardComment:*Attachment:*CheckList:*", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [-580, 310], "id": "60bc897d-49b0-41d9-9ecd-a241e69f23fd"}, {"parameters": {"resource": "board", "name": "TestBoard", "description": "test qa board", "additionalFields": {}}, "name": "Trello", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [-300, 311], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "d9bb9631-be15-44b7-8962-15adb038513f"}, {"parameters": {"resource": "board", "operation": "update", "id": "={{$node[\"Trello\"].json[\"id\"]}}", "updateFields": {"name": "UpatedTestBoard"}}, "name": "Trello1", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [2970, 301], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "5240d7bc-d9d5-465e-87d5-7d68513b0552"}, {"parameters": {"resource": "board", "operation": "get", "id": "={{$node[\"Trello\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello2", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [3120, 301], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "d7d28b3a-a2ec-4c45-aeab-ffd60fa7ed1c"}, {"parameters": {"resource": "board", "operation": "delete", "id": "={{$node[\"Trello\"].json[\"id\"]}}"}, "name": "Trello3", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [3270, 301], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "bfd73a34-44a6-4067-9e68-47f217a6e96a"}, {"parameters": {"resource": "list", "idBoard": "={{$node[\"Trello\"].json[\"id\"]}}", "name": "To Test", "additionalFields": {}}, "name": "Trello4", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [-160, 411], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "f539269f-9813-4158-9cfb-3568adc2ad4a"}, {"parameters": {"resource": "list", "operation": "update", "id": "={{$node[\"Trello4\"].json[\"id\"]}}", "updateFields": {}}, "name": "Trello5", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [-10, 411], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "6cc9eb56-e9ac-48bd-965f-61da503191f8"}, {"parameters": {"resource": "list", "operation": "get", "id": "={{$node[\"Trello4\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello6", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [140, 411], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "9ee41feb-6b2d-4b76-9e39-13a8a41553e6"}, {"parameters": {"resource": "list", "operation": "getAll", "id": "={{$node[\"Trello\"].json[\"id\"]}}", "limit": 1, "additionalFields": {}}, "name": "Trello7", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [290, 411], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "34c2e5be-9ae1-4212-991b-8099e11fedb1"}, {"parameters": {"resource": "list", "operation": "getCards", "id": "={{$node[\"Trello4\"].json[\"id\"]}}", "limit": 1, "additionalFields": {}}, "name": "Trello8", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [2550, 401], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "6ecf2b95-1431-4ca6-85da-a099c2152169"}, {"parameters": {"resource": "list", "operation": "archive", "id": "={{$node[\"Trello4\"].json[\"id\"]}}"}, "name": "Trello9", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [2840, 401], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "92cfc2ec-b2bb-4e00-afae-1a540903886a"}, {"parameters": {"listId": "={{$node[\"Trello4\"].json[\"id\"]}}", "name": "TestCard", "description": "qa test card", "additionalFields": {}}, "name": "Trello10", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [430, 511], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "3ecf077b-90ae-484b-91f7-a134defb5a9b"}, {"parameters": {"resource": "cardComment", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "text": "Test comment"}, "name": "Trello11", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [590, 591], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "f60d1db5-08b7-47d3-8e87-c6d6db863400"}, {"parameters": {"resource": "cardComment", "operation": "update", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "commentId": "={{$node[\"Trello11\"].json[\"id\"]}}", "text": "Updated comment"}, "name": "Trello12", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [740, 591], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "b9b78d60-9ac7-47a7-a21d-91c0eea11529"}, {"parameters": {"resource": "cardComment", "operation": "delete", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "commentId": "={{$node[\"Trello11\"].json[\"id\"]}}"}, "name": "Trello13", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [890, 591], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "ab58cd8f-6d9c-4d13-9061-e7075baa09a0"}, {"parameters": {"operation": "update", "id": "={{$node[\"Trello10\"].json[\"id\"]}}", "updateFields": {"name": "UpdateTestCard"}}, "name": "Trello14", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [2270, 501], "alwaysOutputData": true, "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "4a02293e-8c77-4fec-96d4-b30ef8bef67d"}, {"parameters": {"operation": "get", "id": "={{$node[\"Trello10\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello15", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [2410, 501], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "5f3eecb4-bdc3-41ee-8c08-6133996cfe6c"}, {"parameters": {"operation": "delete", "id": "={{$node[\"Trello10\"].json[\"id\"]}}"}, "name": "Trello16", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [2700, 501], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "4b372bda-f6ba-4148-af60-7baa2750b199"}, {"parameters": {"resource": "label", "operation": "create", "boardId": "={{$node[\"Trello\"].json[\"id\"]}}", "name": "TestLabel", "color": "blue"}, "name": "Trello17", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [590, 431], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "00028ce8-a909-46b7-a39b-2893f039dfb4"}, {"parameters": {"resource": "label", "operation": "update", "id": "={{$node[\"Trello17\"].json[\"id\"]}}", "updateFields": {"color": "orange"}}, "name": "Trello18", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [730, 431], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "4b45bec0-38ea-44fb-b8ce-2bdeb0111749"}, {"parameters": {"resource": "label", "boardId": "={{$node[\"Trello\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello19", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [870, 431], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "a6d224e2-f55b-41a4-9682-27221a17946e"}, {"parameters": {"resource": "checklist", "operation": "create", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "name": "TestCheckList", "additionalFields": {}}, "name": "Trello20", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [590, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "59a94a1d-eb67-48bc-82ea-065bd2ef4458"}, {"parameters": {"resource": "checklist", "operation": "createCheckItem", "checklistId": "={{$node[\"Trello20\"].json[\"id\"]}}", "name": "TestCheckListItem", "additionalFields": {}}, "name": "Trello21", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [740, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "1fd1b5e1-6029-4a2a-972e-43014d5fafc1"}, {"parameters": {"resource": "checklist", "operation": "updateCheckItem", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "checkItemId": "={{$node[\"Trello21\"].json[\"id\"]}}", "additionalFields": {"name": "UpdatedCheckListItem", "state": "complete"}}, "name": "Trello22", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [920, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "8736bed5-3556-44ae-a95f-3bdf353beaab"}, {"parameters": {"resource": "checklist", "operation": "getCheckItem", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "checkItemId": "={{$node[\"Trello21\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello23", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [1090, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "16a9b8ce-2f76-4bf2-89c0-5adaf2f82c13"}, {"parameters": {"resource": "checklist", "operation": "completedCheckItems", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello24", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [1270, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "6e2b2fd6-0fa0-44d8-8530-d95043cf117f"}, {"parameters": {"resource": "checklist", "operation": "getCheckItem", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "checkItemId": "={{$node[\"Trello21\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello25", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [1440, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "0387d97e-31f6-4226-9da6-c029714fd5dd"}, {"parameters": {"resource": "checklist", "operation": "deleteCheckItem", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "checkItemId": "={{$node[\"Trello21\"].json[\"id\"]}}"}, "name": "Trello26", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [1600, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "368bfe8c-797a-4458-90bb-24c54393e038"}, {"parameters": {"resource": "checklist", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello27", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [1740, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "c67011e2-140d-489d-bffd-7e3cd6704fee"}, {"parameters": {"resource": "checklist", "operation": "get", "id": "={{$node[\"Trello20\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello28", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [1890, 281], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "61c6e9e9-d539-43fa-ab8b-836c660a4182"}, {"parameters": {"resource": "checklist", "operation": "delete", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "id": "={{$node[\"Trello20\"].json[\"id\"]}}"}, "name": "Trello29", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [2040, 281], "alwaysOutputData": true, "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "3d78422e-1c16-4ece-8aac-c62178ff0687"}, {"parameters": {"resource": "attachment", "operation": "create", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "url": "https://cdn-images-1.medium.com/max/159/1*<EMAIL>", "additionalFields": {}}, "name": "Trello30", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [590, 751], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "38fa57aa-5631-469e-9c6f-a2f6fa5e87cf"}, {"parameters": {"resource": "attachment", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello31", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [730, 751], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "b6fa8d72-0a6e-471a-bfa9-46c70dc0f1cb"}, {"parameters": {"resource": "attachment", "operation": "get", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "id": "={{$node[\"Trello30\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Trello32", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [890, 751], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "d789d4bf-4a60-43da-963d-41fc5ea923e1"}, {"parameters": {"resource": "attachment", "operation": "delete", "cardId": "={{$node[\"Trello10\"].json[\"id\"]}}", "id": "={{$node[\"Trello30\"].json[\"id\"]}}"}, "name": "Trello33", "type": "n8n-nodes-base.trello", "typeVersion": 1, "position": [1050, 751], "credentials": {"trelloApi": {"id": "41", "name": "Trello creds"}}, "id": "7d6b761b-1dc0-45c6-852e-c5a7d1e7d3d7"}], "connections": {"Trello": {"main": [[{"node": "Trello4", "type": "main", "index": 0}]]}, "Trello1": {"main": [[{"node": "Trello2", "type": "main", "index": 0}]]}, "Trello2": {"main": [[{"node": "Trello3", "type": "main", "index": 0}]]}, "Trello4": {"main": [[{"node": "Trello5", "type": "main", "index": 0}]]}, "Trello5": {"main": [[{"node": "Trello6", "type": "main", "index": 0}]]}, "Trello6": {"main": [[{"node": "Trello7", "type": "main", "index": 0}]]}, "Trello7": {"main": [[{"node": "Trello10", "type": "main", "index": 0}]]}, "Trello8": {"main": [[{"node": "Trello16", "type": "main", "index": 0}]]}, "Trello9": {"main": [[{"node": "Trello1", "type": "main", "index": 0}]]}, "Trello10": {"main": [[{"node": "Trello30", "type": "main", "index": 0}, {"node": "Trello11", "type": "main", "index": 0}, {"node": "Trello17", "type": "main", "index": 0}, {"node": "Trello20", "type": "main", "index": 0}]]}, "Trello11": {"main": [[{"node": "Trello12", "type": "main", "index": 0}]]}, "Trello12": {"main": [[{"node": "Trello13", "type": "main", "index": 0}]]}, "Trello14": {"main": [[{"node": "Trello15", "type": "main", "index": 0}]]}, "Trello15": {"main": [[{"node": "Trello8", "type": "main", "index": 0}]]}, "Trello16": {"main": [[{"node": "Trello9", "type": "main", "index": 0}]]}, "Trello17": {"main": [[{"node": "Trello18", "type": "main", "index": 0}]]}, "Trello18": {"main": [[{"node": "Trello19", "type": "main", "index": 0}]]}, "Trello20": {"main": [[{"node": "Trello21", "type": "main", "index": 0}]]}, "Trello21": {"main": [[{"node": "Trello22", "type": "main", "index": 0}]]}, "Trello22": {"main": [[{"node": "Trello23", "type": "main", "index": 0}]]}, "Trello23": {"main": [[{"node": "Trello24", "type": "main", "index": 0}]]}, "Trello24": {"main": [[{"node": "Trello25", "type": "main", "index": 0}]]}, "Trello25": {"main": [[{"node": "Trello26", "type": "main", "index": 0}]]}, "Trello26": {"main": [[{"node": "Trello27", "type": "main", "index": 0}]]}, "Trello27": {"main": [[{"node": "Trello28", "type": "main", "index": 0}]]}, "Trello28": {"main": [[{"node": "Trello29", "type": "main", "index": 0}]]}, "Trello29": {"main": [[{"node": "Trello14", "type": "main", "index": 0}]]}, "Trello30": {"main": [[{"node": "Trello31", "type": "main", "index": 0}]]}, "Trello31": {"main": [[{"node": "Trello32", "type": "main", "index": 0}]]}, "Trello32": {"main": [[{"node": "Trello33", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Trello", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}