{"createdAt": "2021-02-16T16:30:25.114Z", "updatedAt": "2021-02-16T19:02:02.611Z", "id": "22", "name": "Hubspot:ContactList:add remove:Form:getFields submit", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "89975e09-da30-4c6d-8d61-9650d57b9c3d"}, {"parameters": {"resource": "form", "formId": "a3c94446-bea8-49be-80a7-c862efe7ceb6"}, "name": "Hubspot", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [490, 210], "credentials": {"hubspotApi": {"id": "11", "name": "hubsport api key"}}, "id": "3211fe29-82cf-4b3e-95a8-9a35f9daab8a"}, {"parameters": {"resource": "form", "operation": "submit", "formId": "a3c94446-bea8-49be-80a7-c862efe7ceb6", "additionalFields": {}, "lengalConsentUi": {"lengalConsentValues": {"consentToProcess": true, "text": "test", "communicationsUi": {"communicationValues": []}}}}, "name": "Hubspot1", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [890, 210], "credentials": {"hubspotApi": {"id": "11", "name": "hubsport api key"}}, "id": "dfae8bbd-deb9-4584-b5d0-c054d7c68d45"}, {"parameters": {"resource": "contactList"}, "name": "Hubspot2", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [1220, 290], "credentials": {"hubspotApi": {"id": "11", "name": "hubsport api key"}}, "disabled": true, "id": "c05ef08b-1a37-4e96-a4ed-79d07375d395"}, {"parameters": {"resource": "contactList", "email": "<EMAIL>", "listId": "2"}, "name": "Hubspot3", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [500, 410], "credentials": {"hubspotApi": {"id": "11", "name": "hubsport api key"}}, "id": "d442252a-bb6c-455d-91c0-7e84b2acd2d0"}, {"parameters": {"resource": "contactList", "operation": "remove", "id": "={{$json[\"updated\"][0]}}", "listId": "2"}, "name": "Hubspot4", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [700, 410], "credentials": {"hubspotApi": {"id": "11", "name": "hubsport api key"}}, "id": "80251cfe-7af7-4f16-a8c9-7d9a07ecf296"}, {"parameters": {"functionCode": "return [{json:{\n  email:`test${Date.now().toString().substr(5)}@gmail.com`,\n  firstname:'Test first',\n  lastname:'Test last',\n  message:'Message'\n}}];"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [690, 210], "notesInFlow": true, "notes": "Turn field into single json", "id": "957f2262-d3e3-4f6f-894d-834aa2e12e31"}], "connections": {"Start": {"main": [[{"node": "Hubspot", "type": "main", "index": 0}, {"node": "Hubspot3", "type": "main", "index": 0}]]}, "Hubspot": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Hubspot3": {"main": [[{"node": "Hubspot4", "type": "main", "index": 0}]]}, "Function": {"main": [[{"node": "Hubspot1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}