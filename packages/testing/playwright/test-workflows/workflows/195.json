{"createdAt": "2021-05-03T07:34:19.371Z", "updatedAt": "2021-05-03T07:34:19.371Z", "id": "195", "name": "Kitemaker:Organization:get:Space:getAll:User:getAll:WorkItem:create get getAll update", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "ad2433c4-6d81-4195-a812-f036e3f63181"}, {"parameters": {"resource": "organization"}, "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.kitemaker", "typeVersion": 1, "position": [450, 150], "credentials": {"kitemakerApi": {"id": "159", "name": "Kitemaker API creds"}}, "id": "3714c961-e9eb-4c84-b712-8e30572a0b71"}, {"parameters": {"resource": "space", "limit": 1}, "name": "Kitemaker1", "type": "n8n-nodes-base.kitemaker", "typeVersion": 1, "position": [450, 300], "credentials": {"kitemakerApi": {"id": "159", "name": "Kitemaker API creds"}}, "id": "1e1fb5db-906c-4b93-a411-dfe137207a51"}, {"parameters": {"resource": "user", "limit": 1}, "name": "Kitemaker2", "type": "n8n-nodes-base.kitemaker", "typeVersion": 1, "position": [450, 450], "credentials": {"kitemakerApi": {"id": "159", "name": "Kitemaker API creds"}}, "id": "ab77571b-c9e8-478a-bb3d-e3cda0e7efee"}, {"parameters": {"operation": "create", "title": "=WorkItem{{Date.now()}}", "statusId": "0f42cd20f9617405", "additionalFields": {}}, "name": "Kitemaker3", "type": "n8n-nodes-base.kitemaker", "typeVersion": 1, "position": [450, 600], "credentials": {"kitemakerApi": {"id": "159", "name": "Kitemaker API creds"}}, "id": "67de6dc9-c5c6-464e-b92d-64e839f7ae57"}, {"parameters": {"workItemId": "={{$node[\"Kitemaker3\"].json[\"id\"]}}"}, "name": "Kitemaker4", "type": "n8n-nodes-base.kitemaker", "typeVersion": 1, "position": [600, 600], "credentials": {"kitemakerApi": {"id": "159", "name": "Kitemaker API creds"}}, "id": "623c86bc-c0e6-4974-aede-669fd25b3e49"}, {"parameters": {"operation": "getAll", "spaceId": "0f42cd20b0617400", "limit": 1}, "name": "Kitemaker5", "type": "n8n-nodes-base.kitemaker", "typeVersion": 1, "position": [750, 600], "credentials": {"kitemakerApi": {"id": "159", "name": "Kitemaker API creds"}}, "id": "0282eaab-cfe7-46ab-b0b1-958789c39eb6"}, {"parameters": {"operation": "update", "workItemId": "={{$node[\"Kitemaker3\"].json[\"id\"]}}", "updateFields": {"statusId": "0f42cd20f9617401", "title": "=Updated{{$node[\"Kitemaker3\"].json[\"title\"]}}"}}, "name": "Kitemaker6", "type": "n8n-nodes-base.kitemaker", "typeVersion": 1, "position": [900, 600], "credentials": {"kitemakerApi": {"id": "159", "name": "Kitemaker API creds"}}, "id": "af8e0085-242b-4d5c-9efd-ded439b41aba"}], "connections": {"Start": {"main": [[{"node": "Kitemaker1", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Kitemaker2", "type": "main", "index": 0}, {"node": "Kitemaker3", "type": "main", "index": 0}]]}, "Kitemaker3": {"main": [[{"node": "Kitemaker4", "type": "main", "index": 0}]]}, "Kitemaker4": {"main": [[{"node": "Kitemaker5", "type": "main", "index": 0}]]}, "Kitemaker5": {"main": [[{"node": "Kitemaker6", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}