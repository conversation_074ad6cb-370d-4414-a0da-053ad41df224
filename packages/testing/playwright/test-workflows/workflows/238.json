{"createdAt": "2024-03-04T20:44:21.362Z", "updatedAt": "2024-03-04T20:44:21.362Z", "id": "238", "name": "BasicLLMChain:OpenAIInstruct", "active": false, "nodes": [{"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-3.5-turbo-instruct"}, "options": {"temperature": 0}}, "id": "086e03aa-f941-4dbc-9794-d418893efd48", "name": "OpenAI Model", "type": "@n8n/n8n-nodes-langchain.lmOpenAi", "typeVersion": 1, "position": [660, 600], "credentials": {"openAiApi": {"id": "Zak03cqeLUOsgkFI", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "How much is 1+1? Only provide the numerical answer without any other text.\n\n"}, "id": "3d36ca4b-ca52-4c76-8729-2d1416b97254", "name": "Open AI Instruct", "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [640, 460]}, {"parameters": {}, "id": "6b6a91f3-cb8a-474b-8db8-e4309bd25d92", "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [460, 460]}], "connections": {"OpenAI Model": {"ai_languageModel": [[{"node": "Open AI Instruct", "type": "ai_languageModel", "index": 0}]]}, "When clicking \"Test workflow\"": {"main": [[{"node": "Open AI Instruct", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "c923851b-a7a2-4c7f-895c-d90f994d3e3a", "triggerCount": 0, "tags": []}