{"name": "Test workflow 2", "nodes": [{"parameters": {}, "id": "624e0991-5dac-468b-b872-a9d35cb2c7d1", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [360, 260]}, {"parameters": {"jsCode": "// Loop over input items and add a new field\n// called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "48823b3a-ec82-4a05-84b8-24ac2747e648", "name": "Code", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [580, 260]}], "pinData": {}, "connections": {"On clicking 'execute'": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "hash": "4d2e29ffcae2a12bdd28a7abe9681a6b", "id": 4, "tags": [{"name": "other-tag-1", "createdAt": "2022-11-10T13:45:43.821Z", "updatedAt": "2022-11-10T13:45:43.821Z", "id": "8"}, {"name": "other-tag-2", "createdAt": "2022-11-10T13:45:46.881Z", "updatedAt": "2022-11-10T13:45:46.881Z", "id": "9"}]}