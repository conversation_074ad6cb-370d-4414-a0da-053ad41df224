diff --git a/legacy/build/pdf.mjs b/legacy/build/pdf.mjs
index 1a373ea986beef21076d30438235d180a0487c20..9d584f4fcc13f0b86e57160570c530e5ac794638 100644
--- a/legacy/build/pdf.mjs
+++ b/legacy/build/pdf.mjs
@@ -12060,9 +12060,9 @@ class DOMWasmFactory extends BaseWasmFactory {
 if (isNodeJS) {
   let canvas;
   try {
-    const require = process.getBuiltinModule("module").createRequire(import.meta.url);
+    const _require = process.getBuiltinModule("module").createRequire(import.meta.url);
     try {
-      canvas = require("@napi-rs/canvas");
+      canvas = _require("@napi-rs/canvas");
     } catch (ex) {
       warn(`Cannot load "@napi-rs/canvas" package: "${ex}".`);
     }
@@ -12106,8 +12106,8 @@ async function node_utils_fetchData(url) {
 class NodeFilterFactory extends BaseFilterFactory {}
 class NodeCanvasFactory extends BaseCanvasFactory {
   _createCanvas(width, height) {
-    const require = process.getBuiltinModule("module").createRequire(import.meta.url);
-    const canvas = require("@napi-rs/canvas");
+    const _require = process.getBuiltinModule("module").createRequire(import.meta.url);
+    const canvas = _require("@napi-rs/canvas");
     return canvas.createCanvas(width, height);
   }
 }
