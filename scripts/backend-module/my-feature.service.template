import { Logger } from '@n8n/backend-common';
import { Service } from '@n8n/di';

import { MyFeatureConfig } from './my-feature.config';
import { MyFeatureRepository } from './my-feature.repository';

@Service()
export class MyFeatureService {
	private intervalId?: NodeJS.Timeout;

	constructor(
		private readonly myFeatureRepository: MyFeatureRepository,
		private readonly logger: Logger,
		private readonly config: MyFeatureConfig,
	) {
		this.logger = this.logger.scoped('my-feature' /* Add scope to logging.config.ts */);
	}

	start() {
		this.logger.debug('Starting feature work...');

		this.intervalId = setInterval(
			() => {
				this.logger.debug('Running scheduled task...');
			},
			this.config.taskInterval * 60 * 1000,
		);
	}

	async shutdown() {
		this.logger.debug('Shutting down...');

		if (this.intervalId) {
			clearInterval(this.intervalId);
		}

		await Promise.resolve();
	}

	async getSummary() {
		return await this.myFeatureRepository.getSummary();
	}
}
